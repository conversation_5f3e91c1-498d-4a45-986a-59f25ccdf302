# Firecrawl Company Data Scraper

这个工具使用 Firecrawl 从慕尼黑的酒店和餐饮公司网站抓取结构化数据，并将其转换为符合公司表单字段的格式。

## 功能特点

- 🔍 **智能抓取**: 使用 Firecrawl API 抓取公司网站内容
- 📊 **数据提取**: 自动提取公司信息并映射到表单字段
- ✅ **数据验证**: 验证提取的数据是否符合表单要求
- 🏢 **多类型支持**: 支持酒店、餐厅、餐饮、连锁店等不同类型
- 📁 **结果保存**: 保存原始数据、处理后数据和摘要报告
- 🔄 **重试机制**: 自动重试失败的请求
- ⏱️ **速率限制**: 控制请求频率避免被封

## 安装和设置

### 1. 安装依赖

```bash
cd firecrawl
npm install
```

### 2. 配置环境变量

复制环境变量模板：
```bash
cp firecrawl/.env.example firecrawl/.env
```

编辑 `firecrawl/.env` 文件，添加你的 Firecrawl API 密钥：
```env
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
OUTPUT_DIR=./output
```

### 3. 获取 Firecrawl API 密钥

1. 访问 [Firecrawl.dev](https://firecrawl.dev)
2. 注册账户
3. 获取 API 密钥
4. 将密钥添加到 `firecrawl/.env` 文件

## 使用方法

### 测试数据提取逻辑

首先运行测试脚本验证数据提取逻辑：

```bash
cd firecrawl
npm run test
```

这将使用示例数据测试提取和验证功能。

### 抓取公司数据

运行主要抓取脚本：

```bash
cd firecrawl
npm run scrape
```

脚本将：
1. 依次访问 `firecrawl/company-urls.js` 中定义的公司网站
2. 使用 Firecrawl 抓取网站内容
3. 提取结构化的公司信息
4. 验证数据完整性
5. 保存结果到 `firecrawl/output` 目录

## 输出文件

脚本会在 `firecrawl/output` 目录生成以下文件：

- `scraped-companies-[timestamp].json` - 所有抓取结果（包括失败的）
- `valid-companies-[timestamp].json` - 只包含验证通过的公司数据
- `scraping-summary-[timestamp].json` - 抓取过程摘要报告

## 支持的公司类型

- **hotel** - 酒店
- **restaurant** - 餐厅
- **catering** - 餐饮服务
- **food_chain** - 连锁餐饮
- **brewery_restaurant** - 啤酒屋/餐厅

## 提取的字段

脚本会尝试从网站内容中提取以下字段：

### 必填字段
- `CompanyName` - 公司名称
- `ContactPerson` - 联系人
- `ContactEmail` - 联系邮箱
- `ContactPhone` - 联系电话（德国格式）

### 可选字段
- `CompanyWebsite` - 公司网站
- `NeededPositions` - 招聘职位
- `NumberOfVacancies` - 职位数量
- `WorkLocation` - 工作地点
- `RequiredSkills` - 技能要求
- `EmploymentModel` - 雇佣类型（全职/兼职等）
- `Urgency` - 紧急程度
- `JobDescription` - 职位描述

## 自定义配置

### 添加新的公司网站

编辑 `firecrawl/company-urls.js` 文件，添加新的公司信息：

```javascript
{
  url: "https://company-website.com/careers",
  type: "hotel", // 或其他类型
  name: "Company Name"
}
```

### 调整提取逻辑

编辑 `firecrawl/data-extractor.js` 文件中的提取函数来改进数据提取逻辑。

### 修改配置参数

在 `firecrawl/scrape-companies.js` 中可以调整：
- `MAX_RETRIES` - 最大重试次数
- `DELAY_BETWEEN_REQUESTS` - 请求间隔时间

## 故障排除

### 常见问题

1. **API 密钥错误**
   - 确保 `firecrawl/.env` 文件中的 API 密钥正确
   - 检查 Firecrawl 账户余额

2. **抓取失败**
   - 某些网站可能有反爬虫保护
   - 尝试使用备用 URL
   - 检查网站是否可访问

3. **数据验证失败**
   - 检查提取的数据格式
   - 调整 `firecrawl/data-extractor.js` 中的正则表达式

### 调试模式

设置环境变量启用详细日志：
```bash
cd firecrawl
DEBUG=true npm run scrape
```

## 注意事项

- 请遵守网站的 robots.txt 和使用条款
- 控制抓取频率，避免对目标网站造成负担
- 定期检查和更新公司 URL 列表
- 验证提取的数据准确性

## 许可证

此工具仅用于学习和测试目的。使用时请遵守相关法律法规和网站使用条款。
