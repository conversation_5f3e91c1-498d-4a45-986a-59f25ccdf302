# 快速使用指南

## 1. 安装依赖

```bash
cd scripts/firecrawl
npm install
```

## 2. 配置 API 密钥

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，添加你的 Firecrawl API 密钥
# FIRECRAWL_API_KEY=your_api_key_here
```

## 3. 测试数据提取

```bash
npm run test
```

## 4. 抓取公司数据

```bash
npm run scrape
```

## 5. 提交数据到表单（可选）

```bash
npm run submit
```

## 输出文件位置

所有结果保存在 `output/` 目录：
- `scraped-companies-*.json` - 完整抓取结果
- `valid-companies-*.json` - 验证通过的公司数据
- `scraping-summary-*.json` - 抓取摘要报告

## 支持的公司

当前配置包含慕尼黑的以下类型公司：
- 🏨 酒店 (Hotel Vier Jahreszeiten, Bayerischer Hof, Mandarin Oriental)
- 🍽️ 餐厅 (<PERSON><PERSON><PERSON> Grill, Tan<PERSON>s, Augustiner Bräu)
- 🍴 餐饮服务 (<PERSON><PERSON><PERSON>, Partyservice München)
- 🍕 连锁餐饮 (Nordsee, Vapiano)

## 故障排除

如果遇到问题：
1. 检查 API 密钥是否正确
2. 确保网络连接正常
3. 查看控制台错误信息
4. 检查 `output/` 目录中的日志文件
