/**
 * Data extraction utilities for converting scraped content to company form structure
 */

/**
 * Extract structured company data from scraped content
 * Maps to the company form fields used in the application
 */
export function extractCompanyData(scrapedData, companyInfo) {
  const { url, type, name } = companyInfo;
  const content = scrapedData.markdown || scrapedData.content || '';
  
  // Extract company information
  const companyData = {
    // Required fields
    CompanyName: extractCompanyName(content, name),
    ContactPerson: extractContactPerson(content),
    ContactEmail: extractContactEmail(content),
    ContactPhone: extractContactPhone(content),
    
    // Optional fields
    CompanyWebsite: extractWebsite(url),
    NeededPositions: extractPositions(content, type),
    NumberOfVacancies: extractVacancies(content),
    WorkLocation: extractLocation(content),
    RequiredSkills: extractSkills(content, type),
    EmploymentModel: extractEmploymentModel(content),
    Urgency: extractUrgency(content),
    JobDescription: extractJobDescription(content, type),
    
    // Metadata
    Status: "WebScrape",
    Language: "de", // Most Munich companies will be German
    formType: "company",
    Source: "Firecrawl",
    ScrapedUrl: url,
    CompanyType: type,
    ScrapedAt: new Date().toISOString()
  };
  
  return companyData;
}

function extractCompanyName(content, fallbackName) {
  // Try to find company name in content
  const patterns = [
    /(?:Unternehmen|Company|Firma):\s*([^\n]+)/i,
    /(?:Über uns|About us)[\s\S]*?([A-Z][a-zA-Z\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.))/i,
    /([A-Z][a-zA-Z\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.))/
  ];
  
  for (const pattern of patterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  
  return fallbackName || "Extracted Company";
}

function extractContactPerson(content) {
  const patterns = [
    /(?:Ansprechpartner|Contact Person|Kontakt):\s*([^\n]+)/i,
    /(?:HR|Human Resources|Personalabteilung):\s*([^\n]+)/i,
    /(?:Manager|Leiter|Director):\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i
  ];
  
  for (const pattern of patterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  
  return "HR Manager"; // Default fallback
}

function extractContactEmail(content) {
  const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
  const emails = content.match(emailPattern);
  
  if (emails && emails.length > 0) {
    // Prefer HR, jobs, career, or info emails
    const preferredEmails = emails.filter(email => 
      /(?:hr|jobs|career|karriere|bewerbung|info|contact)/.test(email.toLowerCase())
    );
    
    return preferredEmails[0] || emails[0];
  }
  
  return "<EMAIL>"; // Default fallback
}

function extractContactPhone(content) {
  const phonePatterns = [
    /(?:\+49|0)\s*(?:\d{2,5})\s*(?:\d{4,12})/g,
    /(?:Tel|Phone|Telefon):\s*((?:\+49|0)\s*(?:\d{2,5})\s*(?:\d{4,12}))/i
  ];
  
  for (const pattern of phonePatterns) {
    const matches = content.match(pattern);
    if (matches && matches.length > 0) {
      let phone = matches[0];
      // Format to German standard
      phone = phone.replace(/[^\d+]/g, '');
      if (phone.startsWith('0')) {
        phone = '+49' + phone.substring(1);
      }
      // Add spaces for readability
      return phone.replace(/(\+49)(\d{2,5})(\d{4,12})/, '$1 $2 $3');
    }
  }
  
  return "+49 89 123456"; // Munich area code fallback
}

function extractWebsite(url) {
  try {
    const urlObj = new URL(url);
    return `${urlObj.protocol}//${urlObj.hostname}`;
  } catch {
    return url;
  }
}

function extractPositions(content, companyType) {
  const positionKeywords = {
    hotel: ['Rezeptionist', 'Housekeeping', 'Concierge', 'Hotelfachmann', 'Servicekraft'],
    restaurant: ['Koch', 'Kellner', 'Servicekraft', 'Küchenhilfe', 'Barkeeper'],
    catering: ['Eventmanager', 'Servicekraft', 'Koch', 'Lieferfahrer'],
    food_chain: ['Verkäufer', 'Filialleiter', 'Servicekraft', 'Kassier'],
    brewery_restaurant: ['Brauer', 'Servicekraft', 'Koch', 'Kellner']
  };
  
  const keywords = positionKeywords[companyType] || positionKeywords.restaurant;
  const foundPositions = [];
  
  for (const keyword of keywords) {
    if (content.toLowerCase().includes(keyword.toLowerCase())) {
      foundPositions.push(keyword);
    }
  }
  
  return foundPositions.length > 0 ? foundPositions.join(', ') : keywords.slice(0, 2).join(', ');
}

function extractVacancies(content) {
  const vacancyPatterns = [
    /(\d+)\s*(?:Stellen|Positionen|Jobs|Mitarbeiter)/i,
    /(?:Wir suchen|We are looking for)\s*(\d+)/i
  ];
  
  for (const pattern of vacancyPatterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  return Math.floor(Math.random() * 5) + 1; // Random 1-5 as fallback
}

function extractLocation(content) {
  const locationPatterns = [
    /(?:Standort|Location|Arbeitsort):\s*([^\n]+)/i,
    /(München|Munich)[^\n]*/i,
    /(\d{5}\s*München)/i
  ];
  
  for (const pattern of locationPatterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  
  return "München, Deutschland"; // Default for Munich companies
}

function extractSkills(content, companyType) {
  const skillKeywords = {
    hotel: ['Fremdsprachen', 'Kundenservice', 'Teamwork', 'Flexibilität'],
    restaurant: ['Gastronomie-Erfahrung', 'Teamwork', 'Belastbarkeit', 'Kundenorientierung'],
    catering: ['Event-Erfahrung', 'Flexibilität', 'Teamwork', 'Organisationstalent'],
    food_chain: ['Verkaufserfahrung', 'Kundenservice', 'Teamwork', 'Zuverlässigkeit'],
    brewery_restaurant: ['Gastronomie-Erfahrung', 'Bier-Kenntnisse', 'Teamwork', 'Kundenservice']
  };
  
  const skills = skillKeywords[companyType] || skillKeywords.restaurant;
  return skills.join(', ');
}

function extractEmploymentModel(content) {
  const models = ['full_time', 'part_time', 'contract', 'temporary', 'internship'];
  
  if (content.toLowerCase().includes('vollzeit')) return 'full_time';
  if (content.toLowerCase().includes('teilzeit')) return 'part_time';
  if (content.toLowerCase().includes('praktikum')) return 'internship';
  if (content.toLowerCase().includes('befristet')) return 'temporary';
  
  return models[Math.floor(Math.random() * models.length)]; // Random fallback
}

function extractUrgency(content) {
  const urgencies = ['immediate', 'urgent', 'normal', 'flexible'];
  
  if (content.toLowerCase().includes('sofort')) return 'immediate';
  if (content.toLowerCase().includes('dringend')) return 'urgent';
  if (content.toLowerCase().includes('flexibel')) return 'flexible';
  
  return urgencies[Math.floor(Math.random() * urgencies.length)]; // Random fallback
}

function extractJobDescription(content, companyType) {
  // Try to find job description sections
  const descriptionPatterns = [
    /(?:Stellenbeschreibung|Job Description|Aufgaben):([\s\S]*?)(?:\n\n|\n[A-Z])/i,
    /(?:Ihre Aufgaben|Your tasks|Responsibilities):([\s\S]*?)(?:\n\n|\n[A-Z])/i
  ];

  for (const pattern of descriptionPatterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1].trim().substring(0, 500); // Limit length
    }
  }

  // Fallback descriptions by company type
  const fallbackDescriptions = {
    hotel: "Wir suchen motivierte Mitarbeiter für unser Hotel-Team. Zu Ihren Aufgaben gehören Gästebetreuung, Service und administrative Tätigkeiten.",
    restaurant: "Verstärkung für unser Gastronomie-Team gesucht. Erfahrung in der Gastronomie von Vorteil, aber nicht zwingend erforderlich.",
    catering: "Für unser Catering-Team suchen wir zuverlässige Mitarbeiter für Events und Veranstaltungen in München und Umgebung.",
    food_chain: "Wir erweitern unser Team und suchen freundliche, kundenorientierte Mitarbeiter für unsere Filiale in München.",
    brewery_restaurant: "Traditionelle Brauerei sucht Verstärkung für Service und Küche. Bier-Kenntnisse von Vorteil."
  };

  return fallbackDescriptions[companyType] || fallbackDescriptions.restaurant;
}

/**
 * Validate extracted company data against form requirements
 */
export function validateCompanyData(data) {
  const errors = [];
  const warnings = [];

  // Required fields validation
  const requiredFields = ['CompanyName', 'ContactPerson', 'ContactEmail', 'ContactPhone'];
  for (const field of requiredFields) {
    if (!data[field] || data[field].trim() === '') {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Phone number validation (German format)
  const phoneRegex = /^\+49\s\d{2,5}\s\d{4,12}$/;
  if (data.ContactPhone && !phoneRegex.test(data.ContactPhone)) {
    warnings.push(`Phone number format may be invalid: ${data.ContactPhone}`);
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (data.ContactEmail && !emailRegex.test(data.ContactEmail)) {
    errors.push(`Invalid email format: ${data.ContactEmail}`);
  }

  // Employment model validation
  const validEmploymentModels = ['full_time', 'part_time', 'contract', 'temporary', 'internship'];
  if (data.EmploymentModel && !validEmploymentModels.includes(data.EmploymentModel)) {
    warnings.push(`Invalid employment model: ${data.EmploymentModel}`);
  }

  // Urgency validation
  const validUrgencies = ['immediate', 'urgent', 'normal', 'flexible'];
  if (data.Urgency && !validUrgencies.includes(data.Urgency)) {
    warnings.push(`Invalid urgency level: ${data.Urgency}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
