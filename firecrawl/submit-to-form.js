#!/usr/bin/env node

/**
 * <PERSON>ript to submit scraped company data to the form submission endpoint
 * This simulates companies submitting their information through the website
 */

import { readFileSync, readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import fetch from 'node-fetch';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const OUTPUT_DIR = join(__dirname, 'output');
const FORM_ENDPOINT = 'http://localhost:8888/.netlify/functions/submit-form'; // Local development
// const FORM_ENDPOINT = 'https://your-site.netlify.app/.netlify/functions/submit-form'; // Production

const DELAY_BETWEEN_SUBMISSIONS = 3000; // 3 seconds between submissions

/**
 * Find the most recent valid companies file
 */
function findLatestValidCompaniesFile() {
  try {
    const files = readdirSync(OUTPUT_DIR);
    const validCompaniesFiles = files
      .filter(file => file.startsWith('valid-companies-') && file.endsWith('.json'))
      .sort()
      .reverse();
    
    if (validCompaniesFiles.length === 0) {
      throw new Error('No valid companies files found. Run the scraper first.');
    }
    
    return join(OUTPUT_DIR, validCompaniesFiles[0]);
  } catch (error) {
    console.error('❌ Error finding valid companies file:', error.message);
    process.exit(1);
  }
}

/**
 * Submit a single company to the form endpoint
 */
async function submitCompany(companyData, index, total) {
  try {
    console.log(`[${index + 1}/${total}] Submitting: ${companyData.CompanyName}`);
    
    // Prepare the form data (remove metadata fields)
    const formData = {
      CompanyName: companyData.CompanyName,
      ContactPerson: companyData.ContactPerson,
      ContactEmail: companyData.ContactEmail,
      ContactPhone: companyData.ContactPhone,
      CompanyWebsite: companyData.CompanyWebsite,
      NeededPositions: companyData.NeededPositions,
      NumberOfVacancies: companyData.NumberOfVacancies,
      WorkLocation: companyData.WorkLocation,
      RequiredSkills: companyData.RequiredSkills,
      EmploymentModel: companyData.EmploymentModel,
      Urgency: companyData.Urgency,
      JobDescription: companyData.JobDescription,
      Status: "WebScrape", // Mark as web scraped data
      Language: companyData.Language || "de",
      formType: "company"
    };
    
    const response = await fetch(FORM_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log(`✅ Successfully submitted: ${companyData.CompanyName}`);
      return { success: true, company: companyData.CompanyName, result };
    } else {
      console.error(`❌ Failed to submit ${companyData.CompanyName}:`, result.message);
      return { success: false, company: companyData.CompanyName, error: result.message };
    }
    
  } catch (error) {
    console.error(`💥 Error submitting ${companyData.CompanyName}:`, error.message);
    return { success: false, company: companyData.CompanyName, error: error.message };
  }
}

/**
 * Delay function
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Main submission function
 */
async function main() {
  console.log('🚀 Starting form submission process...');
  
  // Find and load the latest valid companies file
  const validCompaniesFile = findLatestValidCompaniesFile();
  console.log(`📁 Loading companies from: ${validCompaniesFile}`);
  
  let companies;
  try {
    const fileContent = readFileSync(validCompaniesFile, 'utf8');
    companies = JSON.parse(fileContent);
  } catch (error) {
    console.error('❌ Error reading companies file:', error.message);
    process.exit(1);
  }
  
  if (!Array.isArray(companies) || companies.length === 0) {
    console.error('❌ No valid companies found in file');
    process.exit(1);
  }
  
  console.log(`📊 Found ${companies.length} companies to submit`);
  console.log(`🎯 Target endpoint: ${FORM_ENDPOINT}`);
  
  // Ask for confirmation
  console.log('\n⚠️  This will submit real data to the form endpoint.');
  console.log('Make sure the endpoint is correct and you want to proceed.');
  
  // In a real scenario, you might want to add a confirmation prompt here
  // For now, we'll proceed automatically
  
  const results = [];
  
  for (let i = 0; i < companies.length; i++) {
    const company = companies[i];
    
    try {
      const result = await submitCompany(company, i, companies.length);
      results.push(result);
      
      // Add delay between submissions to avoid overwhelming the server
      if (i < companies.length - 1) {
        console.log(`⏳ Waiting ${DELAY_BETWEEN_SUBMISSIONS}ms before next submission...`);
        await delay(DELAY_BETWEEN_SUBMISSIONS);
      }
      
    } catch (error) {
      console.error(`💥 Unexpected error with ${company.CompanyName}:`, error.message);
      results.push({
        success: false,
        company: company.CompanyName,
        error: error.message
      });
    }
  }
  
  // Generate summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n' + '='.repeat(50));
  console.log('📈 SUBMISSION SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Successful: ${successful}/${companies.length}`);
  console.log(`❌ Failed: ${failed}/${companies.length}`);
  
  if (failed > 0) {
    console.log('\n❌ Failed submissions:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.company}: ${r.error}`);
    });
  }
  
  if (successful > 0) {
    console.log('\n✅ Successful submissions:');
    results.filter(r => r.success).forEach(r => {
      console.log(`  - ${r.company}`);
    });
  }
  
  console.log('\n🎉 Form submission process completed!');
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}
