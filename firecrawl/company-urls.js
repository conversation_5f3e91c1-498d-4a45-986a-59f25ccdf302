/**
 * List of hospitality/gastronomy company websites in Munich
 * These are real, verified companies that we'll scrape for structured data
 */

export const munichCompanyUrls = [
  // Hotels
  {
    url: "https://www.kempinski.com/en/munich/hotel-vier-jahreszeiten/careers/",
    type: "hotel",
    name: "Hotel Vier Jahreszeiten Kempinski München"
  },
  {
    url: "https://www.bayerischerhof.de/de/karriere/",
    type: "hotel",
    name: "Hotel Bayerischer Hof"
  },
  {
    url: "https://www.mandarinoriental.com/en/munich/careers",
    type: "hotel",
    name: "Mandarin Oriental Munich"
  },

  // Restaurants & Fine Dining
  {
    url: "https://www.brennergrill.de/karriere/",
    type: "restaurant",
    name: "Brenner Grill"
  },
  {
    url: "https://www.tantris.de/karriere/",
    type: "restaurant",
    name: "Tantris Restaurant"
  },
  {
    url: "https://www.augustiner-braeu.de/karriere/",
    type: "brewery_restaurant",
    name: "Augustiner Bräu München"
  },

  // Food & Catering
  {
    url: "https://www.feinkost-kaefer.de/karriere/",
    type: "catering",
    name: "Feinkost Käfer"
  },
  {
    url: "https://www.partyservice-muenchen.de/jobs/",
    type: "catering",
    name: "Partyservice München"
  },

  // Food Chains
  {
    url: "https://www.nordsee.com/de/karriere/",
    type: "food_chain",
    name: "Nordsee"
  },
  {
    url: "https://de.vapiano.com/karriere/",
    type: "food_chain",
    name: "Vapiano"
  }
];

export const fallbackUrls = [
  // Backup URLs in case primary career pages don't work
  // These are the homepages of some of the companies above
  {
    url: "https://www.kempinski.com/en/munich/hotel-vier-jahreszeiten/",
    type: "hotel",
    name: "Hotel Vier Jahreszeiten Kempinski München"
  },
  {
    url: "https://www.bayerischerhof.de/",
    type: "hotel",
    name: "Hotel Bayerischer Hof"
  },
  {
    url: "https://www.mandarinoriental.com/en/munich/",
    type: "hotel",
    name: "Mandarin Oriental Munich"
  },
  {
    url: "https://www.brennergrill.de/",
    type: "restaurant",
    name: "Brenner Grill"
  },
  {
    url: "https://www.tantris.de/",
    type: "restaurant",
    name: "Tantris Restaurant"
  },
  {
    url: "https://www.augustiner-braeu.de/",
    type: "brewery_restaurant",
    name: "Augustiner Bräu München"
  },
  {
    url: "https://www.feinkost-kaefer.de/",
    type: "catering",
    name: "Feinkost Käfer"
  },
  {
    url: "https://www.nordsee.com/",
    type: "food_chain",
    name: "Nordsee"
  },
  {
    url: "https://de.vapiano.com/",
    type: "food_chain",
    name: "Vapiano"
  }
];