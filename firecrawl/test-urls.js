#!/usr/bin/env node

/**
 * Quick script to test if company URLs are accessible
 */

import { munichCompanyUrls } from './company-urls.js';
import fetch from 'node-fetch';

async function testUrl(companyInfo) {
  const { url, name } = companyInfo;
  
  try {
    console.log(`🔍 Testing: ${name} (${url})`);
    
    const response = await fetch(url, {
      method: 'HEAD',
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (response.ok) {
      console.log(`✅ ${name}: ${response.status} ${response.statusText}`);
      return { success: true, status: response.status, company: name };
    } else {
      console.log(`⚠️  ${name}: ${response.status} ${response.statusText}`);
      return { success: false, status: response.status, company: name, error: response.statusText };
    }
    
  } catch (error) {
    console.log(`❌ ${name}: ${error.message}`);
    return { success: false, company: name, error: error.message };
  }
}

async function main() {
  console.log('🚀 Testing company URLs accessibility...');
  console.log(`📊 Testing ${munichCompanyUrls.length} URLs\n`);
  
  const results = [];
  
  for (const companyInfo of munichCompanyUrls) {
    const result = await testUrl(companyInfo);
    results.push(result);
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n' + '='.repeat(50));
  console.log('📈 URL TEST SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Accessible: ${successful}/${munichCompanyUrls.length}`);
  console.log(`❌ Failed: ${failed}/${munichCompanyUrls.length}`);
  
  if (failed > 0) {
    console.log('\n❌ Failed URLs:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.company}: ${r.error}`);
    });
  }
  
  console.log('\n🎉 URL testing completed!');
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}
