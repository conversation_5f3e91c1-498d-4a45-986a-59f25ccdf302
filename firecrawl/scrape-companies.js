#!/usr/bin/env node

/**
 * Main script to scrape Munich hospitality/gastronomy companies using Firecrawl
 * and extract structured data for the company form
 */

import { config } from 'dotenv';
import FirecrawlApp from '@mendable/firecrawl-js';
import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

import { munichCompanyUrls, fallbackUrls } from './company-urls.js';
import { extractCompanyData, validateCompanyData } from './data-extractor.js';

// Load environment variables
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const FIRECRAWL_API_KEY = process.env.FIRECRAWL_API_KEY;
const OUTPUT_DIR = process.env.OUTPUT_DIR || join(__dirname, 'output');
const MAX_RETRIES = 3;
const DELAY_BETWEEN_REQUESTS = 2000; // 2 seconds

if (!FIRECRAWL_API_KEY) {
  console.error('❌ FIRECRAWL_API_KEY environment variable is required');
  console.error('Please copy .env.example to .env and add your Firecrawl API key');
  process.exit(1);
}

// Initialize Firecrawl
const app = new FirecrawlApp({ apiKey: FIRECRAWL_API_KEY });

// Ensure output directory exists
if (!existsSync(OUTPUT_DIR)) {
  mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Delay function for rate limiting
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Scrape a single company URL with retries
 */
async function scrapeCompany(companyInfo, retryCount = 0) {
  const { url, name, type } = companyInfo;
  
  try {
    console.log(`🔍 Scraping: ${name} (${url})`);
    
    const scrapeResult = await app.scrapeUrl(url, {
      formats: ['markdown', 'html'],
      onlyMainContent: true,
      includeTags: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'div', 'span', 'a'],
      excludeTags: ['nav', 'footer', 'header', 'script', 'style', 'iframe'],
      waitFor: 3000
    });
    
    if (!scrapeResult.success) {
      throw new Error(`Scraping failed: ${scrapeResult.error || 'Unknown error'}`);
    }
    
    console.log(`✅ Successfully scraped: ${name}`);
    return scrapeResult.data;
    
  } catch (error) {
    console.error(`❌ Error scraping ${name}: ${error.message}`);
    
    if (retryCount < MAX_RETRIES) {
      console.log(`🔄 Retrying ${name} (attempt ${retryCount + 1}/${MAX_RETRIES})`);
      await delay(DELAY_BETWEEN_REQUESTS * (retryCount + 1));
      return scrapeCompany(companyInfo, retryCount + 1);
    }
    
    console.error(`💥 Failed to scrape ${name} after ${MAX_RETRIES} attempts`);
    return null;
  }
}

/**
 * Process scraped data and extract company information
 */
function processScrapedData(scrapedData, companyInfo) {
  if (!scrapedData) {
    return null;
  }
  
  try {
    // Extract structured company data
    const companyData = extractCompanyData(scrapedData, companyInfo);
    
    // Validate the extracted data
    const validation = validateCompanyData(companyData);
    
    if (!validation.isValid) {
      console.warn(`⚠️  Validation errors for ${companyInfo.name}:`, validation.errors);
    }
    
    if (validation.warnings.length > 0) {
      console.warn(`⚠️  Validation warnings for ${companyInfo.name}:`, validation.warnings);
    }
    
    return {
      ...companyData,
      validation: validation,
      rawData: {
        markdown: scrapedData.markdown?.substring(0, 1000) + '...', // Truncated for storage
        url: scrapedData.sourceURL || companyInfo.url
      }
    };
    
  } catch (error) {
    console.error(`❌ Error processing data for ${companyInfo.name}:`, error.message);
    return null;
  }
}

/**
 * Save results to files
 */
function saveResults(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  // Save all results as JSON
  const allResultsFile = join(OUTPUT_DIR, `scraped-companies-${timestamp}.json`);
  writeFileSync(allResultsFile, JSON.stringify(results, null, 2));
  console.log(`💾 Saved all results to: ${allResultsFile}`);
  
  // Save valid companies only (for form submission)
  const validCompanies = results.filter(r => r.data && r.data.validation.isValid);
  const validCompaniesFile = join(OUTPUT_DIR, `valid-companies-${timestamp}.json`);
  writeFileSync(validCompaniesFile, JSON.stringify(validCompanies.map(r => r.data), null, 2));
  console.log(`💾 Saved ${validCompanies.length} valid companies to: ${validCompaniesFile}`);
  
  // Save summary report
  const summary = {
    timestamp: new Date().toISOString(),
    totalAttempted: results.length,
    successful: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    valid: validCompanies.length,
    companies: results.map(r => ({
      name: r.company.name,
      url: r.company.url,
      type: r.company.type,
      success: r.success,
      valid: r.data ? r.data.validation.isValid : false,
      errors: r.data ? r.data.validation.errors : r.error ? [r.error] : []
    }))
  };
  
  const summaryFile = join(OUTPUT_DIR, `scraping-summary-${timestamp}.json`);
  writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
  console.log(`📊 Saved summary report to: ${summaryFile}`);
  
  return summary;
}

/**
 * Main scraping function
 */
async function main() {
  console.log('🚀 Starting Munich company scraping...');
  console.log(`📁 Output directory: ${OUTPUT_DIR}`);
  console.log(`🎯 Companies to scrape: ${munichCompanyUrls.length}`);
  
  const results = [];
  
  for (let i = 0; i < munichCompanyUrls.length; i++) {
    const companyInfo = munichCompanyUrls[i];
    
    console.log(`\n[${i + 1}/${munichCompanyUrls.length}] Processing: ${companyInfo.name}`);
    
    try {
      // Scrape the company website
      const scrapedData = await scrapeCompany(companyInfo);
      
      if (scrapedData) {
        // Process and extract structured data
        const processedData = processScrapedData(scrapedData, companyInfo);
        
        results.push({
          company: companyInfo,
          success: true,
          data: processedData,
          scrapedAt: new Date().toISOString()
        });
      } else {
        results.push({
          company: companyInfo,
          success: false,
          error: 'Failed to scrape after retries',
          scrapedAt: new Date().toISOString()
        });
      }
      
    } catch (error) {
      console.error(`💥 Unexpected error processing ${companyInfo.name}:`, error.message);
      results.push({
        company: companyInfo,
        success: false,
        error: error.message,
        scrapedAt: new Date().toISOString()
      });
    }
    
    // Rate limiting delay between requests
    if (i < munichCompanyUrls.length - 1) {
      console.log(`⏳ Waiting ${DELAY_BETWEEN_REQUESTS}ms before next request...`);
      await delay(DELAY_BETWEEN_REQUESTS);
    }
  }
  
  // Save results and generate summary
  console.log('\n📊 Scraping completed! Generating reports...');
  const summary = saveResults(results);
  
  // Print final summary
  console.log('\n' + '='.repeat(50));
  console.log('📈 SCRAPING SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Successful: ${summary.successful}/${summary.totalAttempted}`);
  console.log(`❌ Failed: ${summary.failed}/${summary.totalAttempted}`);
  console.log(`✔️  Valid data: ${summary.valid}/${summary.successful}`);
  console.log(`📁 Output directory: ${OUTPUT_DIR}`);
  
  if (summary.failed > 0) {
    console.log('\n❌ Failed companies:');
    summary.companies.filter(c => !c.success).forEach(c => {
      console.log(`  - ${c.name}: ${c.errors.join(', ')}`);
    });
  }
  
  console.log('\n🎉 Scraping process completed!');
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}
