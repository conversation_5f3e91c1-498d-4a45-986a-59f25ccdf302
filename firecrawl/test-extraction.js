#!/usr/bin/env node

/**
 * Test script to validate data extraction logic with sample data
 */

import { extractCompanyData, validateCompanyData } from './data-extractor.js';

// Sample scraped content for testing
const sampleScrapedData = {
  markdown: `
# Hotel Bayerischer Hof München - Karriere

## Über uns
Das Hotel Bayerischer Hof München ist ein traditionsreiches 5-Sterne-Hotel im Herzen von München.

## Stellenangebote

### Rezeptionist (m/w/d) - Vollzeit
Wir suchen einen freundlichen und kompetenten Rezeptionisten für unser Team.

**Ihre Aufgaben:**
- Gästebetreuung und Check-in/Check-out
- Telefonservice und Reservierungen
- Administrative Tätigkeiten

**Anforderungen:**
- Abgeschlossene Ausbildung im Hotelbereich
- Fremdsprachen (Englisch, Französisch)
- Teamwork und Kundenorientierung
- Flexibilität bei den Arbeitszeiten

### Servicekraft Restaurant (m/w/d) - Teilzeit
Verstärkung für unser Restaurant-Team gesucht.

## Kontakt
**Personalabteilung**
Frau Maria Schmidt
Tel: +49 89 21200
E-Mail: <EMAIL>
Adresse: Promenadeplatz 2-6, 80333 München

Wir freuen uns auf Ihre Bewerbung!
`,
  content: 'Hotel Bayerischer Hof München - Karriere...',
  sourceURL: 'https://www.bayerischerhof.de/karriere'
};

const sampleCompanyInfo = {
  url: 'https://www.bayerischerhof.de/karriere',
  type: 'hotel',
  name: 'Hotel Bayerischer Hof'
};

// Test the extraction
console.log('🧪 Testing data extraction...\n');

try {
  // Extract company data
  const extractedData = extractCompanyData(sampleScrapedData, sampleCompanyInfo);
  
  console.log('📊 Extracted Company Data:');
  console.log('='.repeat(40));
  
  // Display extracted data in a formatted way
  const displayFields = [
    'CompanyName',
    'ContactPerson', 
    'ContactEmail',
    'ContactPhone',
    'CompanyWebsite',
    'NeededPositions',
    'NumberOfVacancies',
    'WorkLocation',
    'RequiredSkills',
    'EmploymentModel',
    'Urgency',
    'JobDescription'
  ];
  
  displayFields.forEach(field => {
    console.log(`${field}: ${extractedData[field]}`);
  });
  
  console.log('\n📋 Metadata:');
  console.log(`Status: ${extractedData.Status}`);
  console.log(`Language: ${extractedData.Language}`);
  console.log(`Source: ${extractedData.Source}`);
  console.log(`Company Type: ${extractedData.CompanyType}`);
  console.log(`Scraped URL: ${extractedData.ScrapedUrl}`);
  
  // Validate the extracted data
  console.log('\n✅ Validation Results:');
  console.log('='.repeat(40));
  
  const validation = validateCompanyData(extractedData);
  
  console.log(`Valid: ${validation.isValid ? '✅ Yes' : '❌ No'}`);
  
  if (validation.errors.length > 0) {
    console.log('\n❌ Errors:');
    validation.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  if (validation.warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    validation.warnings.forEach(warning => console.log(`  - ${warning}`));
  }
  
  // Test with different company types
  console.log('\n🔄 Testing different company types...\n');
  
  const companyTypes = [
    { type: 'restaurant', name: 'Test Restaurant' },
    { type: 'catering', name: 'Test Catering' },
    { type: 'food_chain', name: 'Test Food Chain' },
    { type: 'brewery_restaurant', name: 'Test Brewery' }
  ];
  
  companyTypes.forEach(({ type, name }) => {
    const testInfo = { ...sampleCompanyInfo, type, name };
    const testData = extractCompanyData(sampleScrapedData, testInfo);
    
    console.log(`${type.toUpperCase()}:`);
    console.log(`  Positions: ${testData.NeededPositions}`);
    console.log(`  Skills: ${testData.RequiredSkills}`);
    console.log(`  Description: ${testData.JobDescription.substring(0, 100)}...`);
    console.log('');
  });
  
  console.log('✅ All tests completed successfully!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
  process.exit(1);
}

// Test edge cases
console.log('\n🧪 Testing edge cases...\n');

// Test with minimal content
const minimalData = {
  markdown: 'Company XYZ - We are hiring!',
  content: 'Company XYZ - We are hiring!',
  sourceURL: 'https://example.com'
};

const minimalInfo = {
  url: 'https://example.com',
  type: 'restaurant',
  name: 'Company XYZ'
};

try {
  const minimalExtracted = extractCompanyData(minimalData, minimalInfo);
  const minimalValidation = validateCompanyData(minimalExtracted);
  
  console.log('📊 Minimal Content Test:');
  console.log(`Company Name: ${minimalExtracted.CompanyName}`);
  console.log(`Valid: ${minimalValidation.isValid ? '✅' : '❌'}`);
  console.log(`Errors: ${minimalValidation.errors.length}`);
  console.log(`Warnings: ${minimalValidation.warnings.length}`);
  
} catch (error) {
  console.error('❌ Minimal content test failed:', error.message);
}

console.log('\n🎉 All tests completed!');
