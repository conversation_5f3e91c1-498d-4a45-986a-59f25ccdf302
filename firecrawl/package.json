{"name": "firecrawl-company-scraper", "version": "1.0.0", "description": "Firecrawl scripts to extract structured company data from hospitality/gastronomy websites in Munich", "type": "module", "scripts": {"scrape": "node scrape-companies.js", "test": "node test-extraction.js", "submit": "node submit-to-form.js"}, "dependencies": {"@mendable/firecrawl-js": "^1.0.0", "dotenv": "^16.0.0", "node-fetch": "^3.0.0"}, "devDependencies": {"@types/node": "^20.0.0"}}